'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useLanguage } from '@/lib/context/language-context';
import { TextHoverEffect } from '@/components/ui/text-hover-effect';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';

export default function InvestorsFooter() {
  const { language } = useLanguage();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const linkVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.4,
      },
    },
  };

  // Essential navigation links for investors
  const navigationSections = [
    {
      title: language === 'mn' ? 'НАВИГАЦИ' : 'NAVIGATION',
      links: [
        { href: '/about', label: language === 'mn' ? 'Бидний тухай' : 'About' },
        { href: '/programs', label: language === 'mn' ? 'Хөтөлбөрүүд' : 'Programs' },
        { href: '/investors', label: language === 'mn' ? 'Хөрөнгө оруулагчид' : 'Investors' },
      ]
    },
    {
      title: language === 'mn' ? 'ДЭМЖЛЭГ' : 'SUPPORT',
      links: [
        { href: '/contact', label: language === 'mn' ? 'Холбоо барих' : 'Contact Us' },
        { href: '/privacy', label: language === 'mn' ? 'Нууцлалын бодлого' : 'Privacy Policy' },
        { href: '/terms', label: language === 'mn' ? 'Үйлчилгээний нөхцөл' : 'Terms of Service' },
      ]
    }
  ];

  return (
    <footer className="relative border-t border-primary/20 bg-gradient-to-b from-background to-black overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-0" />

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 opacity-5 z-0">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* TextHoverEffect Background */}
      <div className="absolute inset-0 flex items-center justify-center z-0 pointer-events-none">
        <div className="w-full max-w-2xl h-48 pointer-events-auto relative z-0">
          <TextHoverEffect text="INNOHUB" />
        </div>
      </div>

      {/* Main content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 z-10">
        {/* Navigation Links */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {navigationSections.map((section, sectionIndex) => (
            <motion.div key={section.title} variants={itemVariants}>
              <TextReveal>
                <h3 className="font-semibold text-lg mb-4 text-primary">
                  {section.title}
                </h3>
              </TextReveal>
              <motion.ul className="space-y-3" variants={containerVariants}>
                {section.links.map((link, linkIndex) => (
                  <motion.li key={link.href} variants={linkVariants}>
                    <Link
                      href={link.href}
                      className="text-white/80 hover:text-white transition-colors inline-flex items-center group relative z-20"
                    >
                      <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2 group-hover:bg-primary transition-colors"></span>
                      {link.label}
                    </Link>
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>
          ))}
        </motion.div>

        {/* Copyright and Oyu Intelligence Credit */}
        <motion.div
          className="pt-8 border-t border-primary/20 flex flex-col md:flex-row justify-between items-center"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.div variants={itemVariants}>
            <p className="text-white/60 text-sm">
              © 2024 InnoHub. {language === 'mn' ? 'Бүх эрх хуулиар хамгаалагдсан.' : 'All rights reserved.'}
            </p>
          </motion.div>

          {/* Oyu Intelligence LLC Credit */}
          <motion.div variants={itemVariants} className="mt-4 md:mt-0">
            <p className="text-white/60 text-sm">
              {language === 'mn' ? 'Хөгжүүлсэн:' : 'Developed by'}{' '}
              <Link
                href="https://www.oyu-intelligence.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:text-primary/80 transition-colors font-medium relative z-20"
              >
                Oyu Intelligence LLC
              </Link>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  );
}
